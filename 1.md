2025.01.10

本周工作与进展
动态支撑-清算运营团队
方案规划或评审：
1.  梳理2025 galaxy 项目需要重构的组件 和 页面。详见http://wiki.htzq.htsc.com.cn/pages/viewpage.action?pageId=378714991
前端代码治理：
组件库建设：
本周迭代/开发亮点内容：
1. 初始化utcode-mmi-web项目（迁移自交易单元的前端项目），微前端集成到smartops项目

公共建设
- 优化chrome插件 codereview交互，去掉抽屉的方式，换成dom元素直接融入。
  - 下周测试下deepseek 效果，实现插件 deepseek / glm  切换能力。
- 年终述职整理+ 答辩
风险或重点关注点
--
2025.01.17

本周工作与进展
动态支撑-清算运营团队
方案规划或评审：
1. galaxy2.0 方案设计 评审讨论
前端代码治理：
根据之前梳理需要治理的内容，进行了存量组件代码逻辑梳理- DeliveryDetail、settingDetail、tabComponent
组件库建设：
本周迭代/开发亮点内容：
1. 完成utcode-mmi-web项目（迁移自交易单元的前端项目）微前端集成到smartops项目

公共建设
- 优化chrome插件 codereview交互。
  - 实时文件树展示
  - 可搜索的文件列表
  - 可拖拽的界面
  - 支持deepseek 和 glm 切换

风险或重点关注点
--
2025.01.24

本周工作与进展
动态支撑-清算运营团队
方案规划或评审：
1. galaxy2.0 用户体验设计-细节讨论
前端代码治理：
完成存量组件代码逻辑梳理- DeliveryDetail、settingDetail、tabComponent
组件库建设：
本周迭代/开发亮点内容：

公共建设
- 优化chrome插件 codereview交互。
  - 增加claude 3.5 模型 选择
  - 增加deepseek reasoner 模型 选择
  - 测试多个模型cr效果
  给王轩、杨文坚、郭立恒等多位老师使用体验，
    - 目前来看 基于claude3.5 模型的 CR 效果最好，能提供一些指导性cr建议。
    - deepseek Reasoner 模型使用下来有时候 有点误导信息，还有很多无关痛痒的建议。

风险或重点关注点
--
2025.02.14

本周工作与进展
动态支撑-清算运营团队
方案规划或评审：
1. Galaxy otcd 界面交互升级-运营方案沟通
前端代码治理：
组件库建设：
-  Galaxy FilterForm组件 新增AutoComplete能力
本周迭代/开发亮点内容：

公共建设

- 浏览器自动化方案调研-https://xcg1a1l6ku.feishu.cn/wiki/BcVFwu1phi9Fb7kKe5Bc90P5nDe 
- Codereview + ai 思考。

其他
出差南京，与业务运营境内团队沟通支撑需求。
               与团队内部沟通。
2025.02.21

本周工作与进展
动态支撑-清算运营团队
方案规划或评审：
1. Galaxy otcd 界面交互修改点评审讨论
前端代码治理：
1. 交易单元集成到smartops 问题解决
组件库建设：
-  Galaxy FilterForm组件 优化
本周迭代/开发亮点内容：

公共建设

- 浏览器自动化
  - 和巩禛、杨鹏涛老师一起讨论了 浏览器自动化方案 在数字员工的使用场景和可行性
    - 会议结论：1. browser-use使用公司deepseek api 可行性调研 2. 可行的基础上，进行个人浏览器自动化能力开发集成，替换当前的rpa方案
  - 由于公司内部api格式和目前通用api不一致，需要修改browser-use源码进行兼容性集成，目前已经完成源码初步修改，是否能跑通待验证（公司内部大模型api服务sit环境部署有问题，还在修复，等修复了继续验证）
- ai+代码codereview+ 搜索（待完善，待输出文档）
  - 几种情况
    - 开发者自己，在提交代码，或者 commit 之后进行本地diff 代码codereview
    - reviewer，可以通过浏览器插件gitlab 打开链接后，可以通过ai 自动化codereview
    - 统一数据库存储，方便数据消费。
  - Codereview 效果更好的话，需要加上自然语言或者代码搜索功能
    - 需要实现 代码向量化，并且存入向量数据库，可以通过自然语言进行相关代码搜索，比如 查找登录相关代码，可以搜索到相关代码内容，再结合大模型能力，对搜索到的代码进一步提供优化意见。
    - 这样不仅方便了代码功能搜索，代码片段搜索，对codereview效果也更好，比如监测到相同的代码，可以提示复用某个函数或者 提取公共函数等
    - 进展：本周写了相关demo，目前将本地文件夹下的代码向量化存储，并可以通过自然语言搜索出相关代码片段。（需进一步验证，调试）。

其他

2025.02.28

本周工作与进展
交易台
1. 和开开老师进行交易台任务交接，我参与最近的开发排期是从3.5号开始，当前进行中的任务由开开老师继续完成。
动态支撑-清算运营团队

1. Smartops 项目主子应用通信及菜单问题解决。
2. 迭代开发正常推进。
公共建设

- 浏览器自动化
  - 由于公司内部api格式和目前通用api不一致，需要修改browser-use源码进行兼容性集成，
    - 完成公司内部GLM-130b模型进行了兼容开发，目前已经可以跑通流程，但是效果不如连接外部gpt-4o模型， 提供了一些测试效果给巩禛老师。
    - 进行公司内部deepseek模型的兼容开发，遇到了公司内部deepseek返回的回答格式有问题，返回的思考过程<think> 开头，但是没有</think>结尾，目前大模型开发已经解决，今晚7.30部署。下周继续验证。
- 和周韬、顾胜楠老师进行了代码库相关RAG的研究
  - 我这边对于code-bert、jina-embeddings-v2-base-code 等模型进行了本地部署效果poc验证，效果一般，不及预期
  - 申请了公司内部显卡资源，准备部署一个jina-embedding-v3 模型测试测试效果。
    - 大模型功能使用，涉及到通过java+hadoop hdfs方式上传大模型到目标服务器。目前正在云桌面部署了java 和 hadoop服务，不过上传文件过程中，遇到了问题，大模型相关人员正在解决，下周继续

其他

2025.03.07

本周工作与进展
交易台
1. 代码熟悉，需求熟悉，在cursor的加持下，可以做到更快速的代码熟悉，根据需求查找代码，代码业务逻辑解释说明，代码变动可能涉及得修改范围及影响范围等。
2. 5.4期交易台-港股打新优化 需求开发（开发中）
3. 5.2.1期交易台期-支持Equity Order中美夜盘簿记需求开发（已提测）
动态支撑-清算运营团队

1. Smartops 人员请假，协调galaxy相关前端人员支撑部分工作
2. galaxy组件交互升级+融入global一体化方案调研汇报讨论。
公共建设

- 完成llm_api_proxy nodejs服务开发，主要目标是为了让公司内部大模型api 满足openai 规范标准，可以基于公司内部大模型能力使用外部工具。
  - 当前模型支持saas-doubao-15-pro-32k、saas-deepseek-r1两个模型
  - 目前已经可以实现cline插件和browser-use等外部工具的使用。效果可见如图
  - browser-use使用测试结果来看，saas-doubao-15-pro-32k模型效果更好，deepseek-r1使用起来太慢。
[图片]
- 代码库相关RAG的研究
  - 经过不断尝试，已经完成jina-embedding-v3 模型私有化部署，下周尝试对外提供api服务，看看效果。
[图片]

其他

2025.03.14

本周工作与进展
交易台
交易台5.2期分支整合统一上线（包含5.2.1), 目前4.12号上线。
1. 5.4期交易台-港股打新优化 需求开发（已完成，待提测）
2. 交易台5.2期-产品验收中前端改造项（开发完成、周一提测）
3. 交易台5.2期-pending cross 列表请求改为增量下拉优化（开发完成、周一提测）
4. 交易台5.2期-配合后端hkidr字段修改、spread_price字段新增等优化（开发完成、周一提测）
5. 提测bug修复。

交易台6期需求讲解。https://linkapp.htsc.com.cn/S/005oPO
动态支撑-清算运营团队

1. galaxy组件交互升级方案确认及前端工作量确认，下周开始开发http://wiki.htzq.htsc.com.cn/pages/viewpage.action?pageId=391899554
2. 开发商面试
公共建设

- browser-use
  - 基于公司大模型私有化代码及demot提交，并提供给数字员工巩禛等老师在云桌面使用体验
  - 过程中遇到了一个有趣的问题（云桌面启动browser-use,一直卡在step1循环，但又不报错），经过源码debugger分析，最终定位是云桌面环境下，langchain chatopenai 工具内包含了httpx的服务请求，该请求即使base_url 是 127.0.01 也会被云桌面网络代理拦截，转发到cloudjs.htsc，后续加上http_client配置解决问题
[图片]
- 代码库相关RAG的研究
  - 提供jinai_embedding_v3模型的embedding api服务，目前测试环境，等效果测试完毕后上生产
- 完成codereview api服务初步开发，方便后续多处cr需求集成，主要功能如下
  - GitLab 集成：自动获取并分析合并请求中的代码差异，进行代码审查
  - 直接内容审查：直接提交代码片段进行审查
  - 实时进度更新：在处理审查时流式传输更新，避免等待时间过长。

其他

2025.03.21

本周工作与进展
交易台
1. 5.4期交易台-港股打新优化 需求开发（联调）
2. 交易台5.2期-产品验收中前端改造项（提测）
3. 交易台5.2期-pending cross 列表请求改为增量下拉优化（提测）
4. 交易台5.2期-配合后端hkidr字段修改、spread_price字段新增等优化（提测）
5. 交易台5.6期需求评审，https://linkapp.htsc.com.cn/S/005oPO 目前开发周期为3.31-4.11号
动态支撑-清算运营团队

1. 组件交互升级推进中 
  1. 本周完成：galaxy组件modal 弹框开发，需要支持大小位置可拖拽，并且支持缩小弹框悬浮右下角显示等。
公共建设

- 代码库相关RAG的研究
  - 遇到了embedding api 服务，未使用cuda gpu加速，排查了相关原因，中台提供的显卡是v100的卡，支持的cuda版本是11.x，摸索了了一会，当torch 版本1.31.1 ， transformer 4.34.1  cuda 11.7的时候可以运行cuda，但是jina embedding 模型需要torch> 2.x版本。需要中台那边解决下环境问题了。
- 完成codereview api服务开发优化
  - 支持直接进行内容review 和基于 gitlab mr参数 通过接口获取代码diff，再进行大模型review
  - 数据返回格式统一成sse格式。
- Browser-use测试 容器化部署过程发现缺少python镜像，提了相关需求， @周韬 准备了基础镜像，待下周验证后，即可拿去作为基础镜像
其他
三级认证材料整理
2025.03.28

本周工作与进展
交易台
1. 交易台5.2期  测试过程中若干bug 修复和 产品功能性优化开发。
2. 全局报错提示优化
3. 下周进入交易台六期需求开发，https://linkapp.htsc.com.cn/S/005oPO
最新排期计划见：
[图片]

清算运营团队

1. 组件交互升级推进中 
  1. 完成galaxy弹框组件优化，支持点击同一个按钮，多弹窗实例、限制页面弹窗数量、 最小化弹框悬浮按钮等。进度100% 
  2. 表格组件：进度20%
  3. 筛选器组件：30%
公共建设

- 代码库相关RAG的研究
  - 替换embedding api服务为公网jina embedding v3 api 直接调用
  - 对es检索到的数据添加rerank 重排相关逻辑，添加后当前链路为 query -> query embedding -> es search -> search result -> rerank  -> final result
- Browser-use
  - 本地browser-use 服务功能开发实现，当前服务设计为sse流的方式推送数据，每一个data推送信息包含了当前步骤的基本信息、下一步操作信息，还有当前截图信息等，数据流 data 通过isDone 字段 判断是否完成，完成的data数据信息包含本次task最终信息。（下周出一个具体服务设计文档）
- Advancedtable 高性能表格功能优化，支持
  1. 添加鼠标hover到行，右击回调事件
  2. 点击列(column)的时候，点击回调事件，并且点击表头时可以改变背景色
其他
1. 出差南京，与业务团队进行了交流，参加应知应会考试
2025.04.03

本周工作与进展
交易台
1. 交易台5.2期  测试过程中若干bug 修复
2. 交易台5.4期 前后端联调
3. 交易台六期需求开发（进度30%）https://linkapp.htsc.com.cn/S/005oPO
最新排期计划见：
[图片]

清算运营团队

1. 组件交互升级推进中 
  1. 表格组件：进度30%
公共建设

- 代码库相关RAG的研究
  - 优化embedding model 层代码及代码结构，加入bge embedding api （待调试）
- Browser-use
  - Browser-use 服务架构图设计及文档输出BU服务
- llm api proxy 项目结构优化，新增embedding 统一接口，统一输出格式，支持了bge , 顾盛楠老师使用nestjs改造了项目，加持了一把！
- 更新了cr api 服务，优化了 prompt ，返回结果修改为了 json 格式，具体返回结构可见文档Cr api文档。
其他
1. 协助张开开老师排查 oula 引入xlsx文件报错问题
2. gmail授权调研
3. 开发商面试

2025.04.11

本周工作与进展
交易台
1. 交易台5.4期 过冒烟测试用例，修复相应问题
2. 交易台六期需求开发（进度70%）https://linkapp.htsc.com.cn/S/005oPO
3. 发现历史遗留问题http://pt.htsc/paas/detail.html#/dc/space/1/home/<USER>
4. 本周交易台5.2期需求及5.2.1期优化需求上线。
5. 交易台5.3期场外销售交易簿记需求讲解https://linkapp.htsc.com.cn/S/006sWI，开发周期4.14-5.9号
清算运营团队

1. 组件交互升级推进中 
  1. 表格组件：进度95%  
  2. 筛选器： 90%，后续等后端把数据返回定义好再进行优化
  3. 处理resizable弹框组件 最小化后，多个弹框按钮点击顺序错乱bug
公共建设

- 代码库相关RAG的研究
  - Project indexing 接入bge  embedding api，当前支持jina 和bge 两种embedding 模型。
  - 优化llm proxy api 服务代码，丰富接口文档，接口详情见http://proxyllm.sit.saas.htsc/api/docs/#/
- Browser-use
  - 编写服务编译和启动脚本，提供姚窅进行流水线搭建，目前流程走通，姚窅还需改下paas部署脚本
- Advanced table优化- onscroll 事件添加是否纵向滚动判断
其他
1. 开发商面试
2025.04.18

本周工作与进展
交易台

1. 交易台5.4期提测，若干测试bug 修复
2. 完成交易台六期需求前后端联调，正在冒烟测试，准备下周提测 需求文档如：https://linkapp.htsc.com.cn/S/005oPO
3. 交易台5.3期场外销售交易簿记需求 待开发，需求文档https://linkapp.htsc.com.cn/S/006sWI，开发周期4.14-5.9号
清算运营团队

1. 组件交互升级推进中 
  1. 表格组件：进度100%  
公共建设

- 代码库相关RAG的研究
  - 优化llm proxy api 服务代码，超时提升到120s
- Browser-use
  - 跑通流水线，添加数据反写逻辑，联调。
其他
1. 三级答辩材料准备 及 三级答辩
2025.04.25

本周工作与进展
交易台

1. 交易台5.4期提测，若干测试bug 修复，已完成全部bug修复
2. 完成交易台六期需求前后端联调，正在冒烟测试，后端接口调用DQS有点问题，新增DQS查询需求，影响暗盘成交取消时间，后端优化ing
3. 交易台5.3期场外销售交易簿记需求 开发中，需求文档https://linkapp.htsc.com.cn/S/006sWI，开发周期4.14-5.16号
清算运营团队

1. 组件交互升级推进中 
  1. 表格组件：进度100%  
  2. 自定义列模板组件：进度100%
  3. 表格筛选组件：进度100%
公共建设

- 代码库相关RAG的研究
  - 优化cr api 服务，sse 只推增量数据，不包含所有处理结果，优化prompt
- Browser-use
  - 修改executionid 获取逻辑，服务接口联调等
  - 调研browser-use 运行中agent 取消方案、 actions 回放能力、多agent 用户决策 暂停继续等需求。
其他

2025.04.30

本周工作与进展
交易台

1. 交易台5.4期少数几个测试bug修复。
2. 完成交易台六期需求前后端联调，冒烟测试ing，待后端接口问题解决。
3. 交易台5.3期场外销售交易簿记需求 开发中，需求文档https://linkapp.htsc.com.cn/S/006sWI，开发周期4.14-5.16号
清算运营团队

1. 组件交互升级推进中 
  1. 接口联调中，有部分接口还没完成
公共建设

- 代码库相关RAG的研究
  - 优化cr api 服务，优化prompt
- Browser-use
  - 进行browser-use 运行中agent 取消方案、 actions 回放能力 poc验证 （进行中）
其他
Oula 技术创新奖申报 答辩。
2025.05.09

本周工作与进展
交易台

1. 交易台5.3期场外销售交易簿记需求 开发完成，下周进行前后端联调，需求文档https://linkapp.htsc.com.cn/S/006sWI
2. 交易台六期和七期需求及优化需求评审，排期评估等
清算运营团队

1. 组件交互升级推进中 
  1. 接口联调完成，准备测试
公共建设

- Llm api 转发服务@ 张宇 老师支持了 deepseek-v3模型  接入

其他

2025.05.16

本周工作与进展
交易台

1. 交易台5.3期场外销售交易簿记需求开发及联调完成，准备提测，需求文档https://linkapp.htsc.com.cn/S/006sWI
清算运营团队

1. 组件交互升级推进中 
  1. 产品验收和 问题修复
2. 解决小助手智能问答的流式显示的问题
公共建设

- 升级browser-use 服务 到0.1.47，升级依赖，直接基于源码开发，支持任务取消能力。
  - 切换到saas-deepseek-v3 模型
- 文档知识库相关调研
其他

2025.05.23

本周工作与进展
交易台

1. 交易台5.3期场外销售交易簿记需求开发及联调完成，项目经理排期调整，暂不提测，需求文档https://linkapp.htsc.com.cn/S/006sWI
2. 交易台七期需求开发包括dvp优化及批量下单需求开发中，需求文档https://linkapp.htsc.com.cn/S/006dh6 排期5.19-6.20
3. 交易台六期暗盘取消需求提测，测试bug修复
清算运营团队

1. 组件交互升级推进中 
  1. 产品验收和 问题修复
公共建设

- browser use 流水线问题定位排查修复
- ai proxy 服务优化 。
  - 新增日志上报服务， 文档可见http://10.102.74.140:3000/api/docs/#/
  - 新增env proxy npm包 http://web.npm.htsc/package/@ht/ai-env-proxy


其他
参加AIcon 人工智能开发者大会，待整理相关材料。
2025.05.31

本周工作与进展
交易台

1. 交易台七期需求开发包括dvp优化及交易批量下单需求开发中，需求文档https://linkapp.htsc.com.cn/S/006dh6 排期5.19-6.20
2. 交易台六期暗盘取消需求提测，测试bug修复，已完成，待发布
清算运营团队

1. 组件交互升级推进中 
  1. 产品验收和 问题修复
2. 首页动态组件化配置功能开发（使用ai-coding，效果比较好，对于从0-1生成项目提效明显）
http://wiki.htzq.htsc.com.cn/pages/viewpage.action?pageId=406983912
公共建设

- browser use 服务需求开发和联调
  - rerun history 需求开发。
  - 基于kafka 实现分步骤 cancel 需求
  - 接口支持流式和非流式数据返回
- Ai report server 优化： 修改日志轮转为日切
- Advanced table组件 - 新增列背景高亮控制
其他

2025.06.06

本周工作与进展
交易台

1. 交易台七期需求开发包括dvp优化及交易批量下单需求开发中，需求文档https://linkapp.htsc.com.cn/S/006dh6 排期5.19-6.20
  1. dvp完成开发联调，正在冒烟
  2. 新增三个小优化需求，跟随dvp需求排期，已开发完成
  3. 批量下单功能开发中，预计下周完成并进行联调
2. 六期暗盘需求优化变更，暗盘列表切换菜单到holiticview
3. 5.4港股打新需求上线
清算运营团队

1. 组件交互升级推进中 
  1. 产品验收
2. 首页动态组件化配置功能开发
  1. 框架工程脚手架搭建，进度100%
  2. 编辑状态下整体布局，包括页面栅格化拖拽、缩放与自动排版，支持卡片的添加与删除，进度100%
  3. 左侧组件导航菜单开发，支持添加、删除，与右侧画布区域的联动，进度100%
公共建设

1. Browser-use服务数字员工工程化 需求联调测试中，修复相关问题和优化。
其他
出差南京与许喆老师进行业务运营中心工作交接 http://wiki.htzq.htsc.com.cn/pages/viewpage.action?pageId=406983607

2025.06.13

本周工作与进展
交易台

1. 交易台七期需求开发包括dvp优化及交易批量下单需求开发中，需求文档https://linkapp.htsc.com.cn/S/006dh6 排期5.19-6.20
  1. dvp需求测试中，bug修复
  2. 批量下单功能开发完成，下周联调
2. 交易台六期暗盘预计6.28号上线，下周回归测试
清算运营团队

1. 组件交互升级推进中 
  1. 产品验收
2. 首页动态组件化配置功能开发中

公共建设

1. Browser-use服务功能优化
  1. 在自动化测试过程中，如果遇到新开的tab，当前browser-use代码中browser state会默认保留在原tab上，导致了截图都是一样的，但agent行为是正常推进的，优化代码，默认browser state切换至新tab
  2. 偶发window系统  UnicodeEncodeError: 'gbk' codec can't encode character ... 错误，根本原因在于文字编码不匹配。 强制使用utf-8 后解决问题’
2. AI infra
Task Master调研

其他


2025.06.20

本周工作与进展
交易台

1. 交易台七期需求开发包括dvp优化及交易批量下单需求开发中，需求文档https://linkapp.htsc.com.cn/S/006dh6 排期5.19-6.20
  1. dvp需求测试中，bug修复
  2. 批量下单功能开发联调
2. 交易台六期暗盘、七期dvp需求 加几个小优化预计6.28号上线，下周回归测试
3. 交易台改单页面，最大可买数量不动态更新 历史遗留生产bug修复
清算运营团队

1. 组件交互升级推进中 
  1. 产品验收
2. 首页动态组件化配置功能开发中
公共建设

1. Browser-use服务功能优化
  1. 优化代码，默认窗口调整为1920*1100
  2. 新增 浏览器进程崩溃，新的请求自重启浏览器进程能力
2. AI infra
  1. Ai review agent 优化
    1. 增加Summary模式：一次性分析所有文件，智能文件优先级排序和
  内容裁剪提供综合性审查报告（还在效果自测，目前来看，提出的comment 意见较有价值。）

其他

2025.06.27

本周工作与进展
交易台

1. 交易台七期 批量下单 联调及冒烟测试  （下周提测）
2. 交易台本周上线内容回归测试，及生产历史遗留bug修复等，本周具体上线内容如下:
  1. 暗盘成交取消
  2. DVP账户资产查询
  3. 前端优化：查询onlymine适配        
  4. 前端优化：订单创建时，在订单创建接口同时传入订单和成交
  5. 历史遗留生产问题修复：  GTP-22471   GTP-21439
业务运营中心

globo（金星、木星）
1. ftl模板 转pdf 样式问题优化，当前是后端使用的itextPdf 5.x版本，导致word-break样式不生效
  1. 升级itextPdf 到 7.x版本可以解决问题，但是影响比较大，暂搁置
  2. 最终解决方案先 后端加换行符。
2. 个性化配置优化一期修改因人力问题可能需延迟到7.18版本
境内清算
1. 首页动态组件化配置功能开发中, 下周开始接口联调
2. galaxy系统Q3 菜单样式改版及 页面交互升级方案 评估
平台
1. 数据血缘关系图前端组件调研，还是使用antv g6，自定义节点实现。
公共建设

1. Browser-use配合数字员工上线计划准备

其他

2025.07.04

本周工作与进展
交易台

1. 批量下单需求冒烟转测，目前测试中，测试bug修复， 需求详见https://linkapp.htsc.com.cn/S/006dh6
2. 临时上线版本7.19 相关优化需求开发
- 日常盯盘中今日订单改单， dvp不提供最大可买数量试算
- Trade下单、改动等设计交易试算补充ord prop和op order way的默认传值，适配费用计算
业务运营中心

globo（金星、木星）
1. Globo 二期前端优化评审。http://wiki.htzq.htsc.com.cn/pages/viewpage.action?pageId=404884545
2. IPO BSS 改造方案评估，代码改造多tab保存，由单页保存改造为全部保存
境内清算
1. 动态化配置首页 需求开发（优先级降低，待后端接口提供后再继续）
2. 境外页面改造，梳理了需要改造的页面，及改造推进计划。详细文档https://linkapp.htsc.com.cn/S/00kuJh
  1. 交易合约查询EST页面，包括Swap和Option，进度100%，待和后端接口联调。
  2. 异常警示列表-交易捕获，进度100%，待联调。
平台
1. 数据血缘关系图前端组件调研，使用antv g6，自定义节点实现，poc 完成，正在组件开发中。

公共建设

1. Browser-use
  1. 功能新增：历史回放 添加实时状态的截图和状态 
  2. 本周暂未上线，数字员工还是走直接申请llm api方案，不再进行hiagent api适配

其他
1. 业务运营清算，境外页面改造涉及了几十个页面代码重构（旧的组件替换新的组件）
  1. 尝试引入vscode 插件ai 提效，目前尝试来看，deepseek-v3的效果不如claude 3.5，下周再试试使用deepseek-v3精调prompt，看看最终能不能达到    AI 自动化重构代码> 开发商review ai代码 -> 修改少部分代码 -> 完成重构。
2025.07.11

本周工作与进展
交易台

1. 交易台批量下单需求测试中，测试bug修复， 需求详见https://linkapp.htsc.com.cn/S/006dh6
2. 交易台需求评审：
  1. 【交易台2025Q2-7】美股夜盘（1.0不含行情 http://wiki.htzq.htsc.com.cn/pages/viewpage.action?pageId=403697599
  2. 【交易台2025Q2-8】交易台用户调研一期优  http://wiki.htzq.htsc.com.cn/pages/viewpage.action?pageId=409638080
3. 交易台临时版本需求开发完成（7.19上线），转测，http://wiki.htzq.htsc.com.cn/pages/viewpage.action?pageId=414444329
业务运营中心

globo（金星、木星）
1. 调研了GloboUI 虚拟Table在个性化二期是否满足开发需求
2. 个性化表单问题优化
3. 问题定位修复: 先进别的子应用再进scct的payment页面，跳转到详情页关闭页签时缓存未清除。
境内清算
1. 动态化配置首页 需求开发（优先级降低，待后端接口提供后再继续）
2. 境外页面改造，梳理了需要改造的页面，及改造推进计划。详细文档https://linkapp.htsc.com.cn/S/00kuJh
  1. 用AI完成 70多个境外页面的初步改造，进度100%。
  2. 基于AI改造的代码，进行review 和功能完善。
开发商日常问题支撑。

公共建设

1. web 助手产品
  1. 参与需求讨论、规划等
  2. 技术实现储备调研。

其他
1. 业务运营清算，境外页面改造涉及了70多个页面代码升级改造
  1. 重构推进思路： 新组件开发 ->  基于AI完成代码初步改造 -> 人工review + 完善代码 -> 完成需求开发。
  2. AI介入主要思路：  基于 Claude-4 大模型进行初步效果测试与验证，建立基准 -> 引入vscode haicode 插件 -> 分析 Claude 的 thinking 推理过程，基于其思维链条对 DeepSeek-v3 模型进行 prompt 精细化调优 -> 效果达标验证 -> 实际应用
  3. 效果：节省50%前端排期。
2025.07.18

本周工作与进展
交易台

1. 交易台批量下单需求测试中，测试bug修复，测试进度基本完成。 需求详见https://linkapp.htsc.com.cn/S/006dh6
2. 交易台美股夜盘需求开发，http://wiki.htzq.htsc.com.cn/pages/viewpage.action?pageId=403697599
  
  美股夜盘加优化需求版本计划（https://linkapp.htsc.com.cn/S/000lm0）：
  -功能开发联调：7.14-8.8
  -功能测试：8.4-8.22
  -产品验收+业务验收：8.25-9.5
  
3. 交易台临时版本需求上线，http://wiki.htzq.htsc.com.cn/pages/viewpage.action?pageId=414444329
业务运营中心

globo（金星、木星）
1. 个性化表单组件库升级，增加自定义表单和排序功能埋点上报
2. GloboUI 前端一期交互优化基本完成，本周上线
境内清算
1. 境外页面改造，梳理了需要改造的页面，及改造推进计划。详细文档https://linkapp.htsc.com.cn/S/00kuJh
  1. 基于AI改造的代码，进行review 和功能完善进度100%。
开发商日常问题支撑。

公共建设

1. web 助手产品
  1. 参与需求讨论、需求规划、架构设计 架构设计
2. Browser-use 自动化产品上线
  1. 数科那边部署的local-deepseek-v3模型 不支持tools call，重新申请了saas-deepseek-v3
3. 香港部署前端相关文档编写http://wiki.htzq.htsc.com.cn/pages/viewpage.action?pageId=416882796

其他

2025.07.25

本周工作与进展
交易台

1. 交易台动态布局调研，相关demo可见http://10.102.68.171:3000/demos
2. 交易台美股夜盘需求开发，http://wiki.htzq.htsc.com.cn/pages/viewpage.action?pageId=403697599
  本次需求主要新增美股夜盘交易功能，通过调整交易时段、订单类型和账户权限校验等，优化亚洲投资者跨时区交易体验，提升平台竞争力和市场占有率。
3. 优化需求（交易台自定义融资比例时，前端计算融资利息时取消融资利息上限的约束） 上线
业务运营中心

globo（金星、木星）
1. 组件库开发：
  1. GloboLayout SimpleQuery CommonList组件的ts类型优化
  2. 个性化配置不可禁用的项需要支持排序
2. GloboUI 前端一期交互优化遗留问题优化
3. ftl模板转pdf问题定位修复
  1. 问题：后端itext升级到了7 但是最终的pdf  overflow-wrap, word-break  css 属性 样式没生效。
  2. Itext 版本不对，根据 iText 官方的知识库文档，从 iText Core 7.1.14 和 pdfHTML 3.0.3 版本开始，才完整地支持了 overflow-wrap 和 word-break 属性 https://kb.itextpdf.com/itext/pdfhtml-support-for-overflow-wrap-word-break-css-p
境内清算
1. 境外页面前端交互改造正常推进，详细文档https://linkapp.htsc.com.cn/S/00kuJh
2. Tarus 微前端qiankun 子应用支持keepalive

开发商日常问题支撑。

公共建设

1. web 助手产品
  1. 技能定义框架设计(待完善文档） 智能体框架设计
2. Browser-use 自动化产品上线
  1. 生产问题定位
3. 代码质检
  1. 优化prompt ，屏蔽 黄色（警告）级别问题

其他
AI辅助实践输出：
1.    AI Coding 辅助 PoC 验证实践
2. Haicode提效开发实践-清算团队前端交互优化升级
3. 需求转码实践- 交易台
2025.08.01
总结
本周工作总结： 本周主要工作集中
- 交易台美股夜盘功能开发、用户调研优化需求开发。
- 交易台需求转码 新工作流方式探索实践。
- Web助手产品方案设计。
- 业务运营中心常规技术支撑工作。
下周工作计划：
- 交易台用户调研1期优化需求测试验证
- Web助手产品开发
- 需求转码 新工作流方式调优
风险、问题和建议反馈：
- 美股夜盘与批量下单需求合并上线，需要确保测试覆盖充分
- 全球交易平台大屏地图丢失问题需要持续监控验证
重点业务支撑
1、交易台
工作项1: 交易台美股夜盘需求开发
- 概述： 新增美股夜盘交易功能，通过调整交易时段、订单类型和账户权限校验等，优化亚洲投资者跨时区交易体验，提升平台竞争力和市场占有率。需求文档：http://wiki.htzq.htsc.com.cn/pages/viewpage.action?pageId=403697599
- 进展： 开发完成，冒烟测试通过，已与批量下单需求合并上线排期
- 下周计划： 配合测试团队进行完整测试验证，准备上线
- 风险、问题和建议反馈： 合并上线需要确保两个功能模块兼容性

工作项2: 交易台用户调研1期优化需求
- 概述： 针对全量客户迁移后核心用户调研，优化客户信息区搜索与展示、下单类型展示、交易簿记历史单新增、页面标识、关键数据实时性等18项功能，以提升交易员业务效率和服务质量。需求文档：http://wiki.htzq.htsc.com.cn/pages/viewpage.action?pageId=409638080
- 进展： 开发80%，计划8月8号提测
- 下周计划：开发完成提测
- 风险、问题和建议反馈： 暂无

工作项3：需求转码实践
- 概述：需求转码 交易台实践中，美股夜盘的代码已经开发完成，我用之前需求详细设计 + codereview prompt 使用了 Augment 辅助进行了开发商代码质检
- 进展：完成使用Augment辅助进行美股夜盘代码质检，结合需求详细设计文档和codereview prompt
- 结论： AI返回的建议全部有用，特别是bug发现，验证了AI辅助开发的有效性，具体可见文档优化步骤第四步：结合设计文档进行高效Code Review内容 需求转码实践- 交易台 

2、业务运营中心技术支撑
业务运营中心常规技术问题支撑。
工作项1: 境外页面前端交互改造
- 概述： 境内清算境外页面交互体验优化改造
- 技术方案： 详细文档：https://linkapp.htsc.com.cn/S/00kuJh
- 进展： 正常推进中
- 下周计划： 继续推进开发进度
- 风险、问题和建议反馈： 无

工作项2：全球交易平台大屏地图丢失问题排查
- 概述： 背景/目标描述：解决大屏长时间运行后地图突然消失的技术问题
- 过程： 初步定位长时间运行后，浏览器可能会回收Canvas上下文以释放GPU内存，导致地图突然消失
- 结论： 已制定解决方案：加入canvas上下文监听机制，持续监控验证效果
平台能力建设

事项1<建设性>: Web助手产品
- 概述： 背景/目标描述：构建智能Web助手产品，提升开发效率和用户体验
- 产品设计： 完成技能开发框架设计，支持节点配置编排
- 技术方案： 完成技能开发框架开发、接口定义、测试环境部署（一期直接编排节点配置，复杂能力后续扩展）
- 进展： 方案设计、排期多轮讨论完成，框架开发完成（是否使用暂定）
- 效果： 为后续智能体功能扩展奠定技术基础

事项2：智能体prompt调试
- 概述： 背景/目标描述：优化Web助手产品的智能体总结能力
- 过程： 研究Gemini CLI中的总结提示词设计模式
- 结论： 发现总结提示词设计相对简单，为后续优化提供参考方向，后续再调研其他总结智能体的prompt及设计方案
调研评测
1. 熟悉了Bmad-Method ，待进一步深入调研和 demo测试。https://github.com/bmadcode/BMAD-METHOD



